import { Inject, Provide } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize, QueryTypes, Transaction } from 'sequelize';
import { CustomError } from '../error/custom.error';
import { Response } from '../entity/response.entity';
import { Answer } from '../entity/answer.entity';
import { Questionnaire } from '../entity/questionnaire.entity';
import { QuestionnaireStatistics } from '../entity/questionnaire-statistics.entity';
import { IncompleteStudentsCache } from '../entity/incomplete-students-cache.entity';
import { GradeStatistics } from '../entity/grade-statistics.entity';
import { ClassStatistics } from '../entity/class-statistics.entity';
import { TeacherStatistics } from '../entity/teacher-statistics.entity';
import {
  SchoolStatisticsQueryDTO,
  TeacherStatisticsQueryDTO,
  TeacherRankingQueryDTO,
  TrendAnalysisQueryDTO,
  IncompleteStudentsQueryDTO,
} from '../dto/statistics.dto';

import {
  ISchoolStatistics,
  ITeacherStatistics,
  ITeacherRankingResponse,
  IIncompleteStudentsSummary,
  IIncompleteStudent,
  IIncompleteStudentsByClass,
  IIncompleteStudentsResponse,
} from '../interface';
import { Custome } from './api_sso/custome.service';
import { Context } from '@midwayjs/koa';

@Provide()
export class StatisticsService {
  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  @InjectRepository(Answer)
  answerRepository: Repository<Answer>;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @InjectRepository(QuestionnaireStatistics)
  statisticsRepository: Repository<QuestionnaireStatistics>;

  @InjectRepository(IncompleteStudentsCache)
  incompleteStudentsCacheRepository: Repository<IncompleteStudentsCache>;

  @InjectRepository(GradeStatistics)
  gradeStatisticsRepository: Repository<GradeStatistics>;

  @InjectRepository(ClassStatistics)
  classStatisticsRepository: Repository<ClassStatistics>;

  @InjectRepository(TeacherStatistics)
  teacherStatisticsRepository: Repository<TeacherStatistics>;

  @InjectDataSource()
  sequelize: Sequelize;

  @Inject()
  customeService: Custome;

  @Inject()
  ctx: Context;

  /**
   * 获取学校维度统计
   * @param queryDto 查询条件
   * @returns 学校统计数据
   */
  async getSchoolStatistics(
    queryDto: SchoolStatisticsQueryDTO
  ): Promise<ISchoolStatistics> {
    const {
      sso_school_code,
      month,
      start_month,
      end_month,
      include_trend,
      include_teacher_ranking,
    } = queryDto;

    // 构建基础查询条件
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code };

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    } else if (start_month && end_month) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = start_month;
      replacements.end_month = end_month;
    }

    // 基础统计查询 - 修正为按学生统计而不是按响应记录统计
    const basicStatsQuery = `
      SELECT
        q.sso_school_code,
        q.sso_school_name,
        COUNT(DISTINCT r.id) as total_responses,
        COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.sso_student_code END) as completed_students,
        COUNT(DISTINCT r.sso_student_code) as total_students_with_responses,
        ROUND(AVG(r.school_rating), 2) as school_average_score,
        ROUND(AVG(r.total_average_score), 2) as teacher_average_score,
        COUNT(DISTINCT a.sso_teacher_id) as total_teachers_evaluated
      FROM questionnaires q
      LEFT JOIN responses r ON q.id = r.questionnaire_id
      LEFT JOIN answers a ON r.id = a.response_id
      WHERE ${whereClause}
      GROUP BY q.sso_school_code, q.sso_school_name
    `;

    const [basicStats] = (await this.sequelize.query(basicStatsQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    if (!basicStats) {
      throw new CustomError('未找到该学校的统计数据');
    }

    // 获取学校所有学生数据
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: sso_school_code,
    });
    const totalStudents = allStudents.length;

    // 重新计算基于学生总数的完成率
    const completedStudents = parseInt(basicStats.completed_students) || 0;
    const accurateCompletionRate =
      totalStudents > 0
        ? Math.round((completedStudents / totalStudents) * 100 * 100) / 100
        : 0;

    const result: ISchoolStatistics = {
      sso_school_code: basicStats.sso_school_code,
      sso_school_name: basicStats.sso_school_name,
      month: month,
      total_responses: parseInt(basicStats.total_responses) || 0,
      completed_responses: completedStudents, // 修正：这里应该是完成的学生数，不是响应数
      total_students: totalStudents,
      completion_rate: accurateCompletionRate,
      school_average_score: parseFloat(basicStats.school_average_score) || 0,
      teacher_average_score: parseFloat(basicStats.teacher_average_score) || 0,
      total_teachers_evaluated:
        parseInt(basicStats.total_teachers_evaluated) || 0,
    };

    // 如果需要趋势数据
    if (include_trend && start_month && end_month) {
      result.response_trend = await this.getResponseTrend(
        sso_school_code,
        start_month,
        end_month
      );
    }

    // 如果需要教师排名
    if (include_teacher_ranking) {
      const rankingResult = await this.getTeacherRanking({
        sso_school_code,
        month,
        page: 1,
        limit: 10,
        sort_by: 'average_score',
        sort_order: 'DESC',
      });
      result.teacher_ranking = rankingResult.list;
    }

    // 获取未填写学生统计（如果有指定月份）
    if (month) {
      result.incomplete_students_summary =
        await this.getIncompleteStudentsSummary(
          sso_school_code,
          undefined,
          month
        );
    }

    return result;
  }

  /**
   * 获取教师维度统计
   * @param queryDto 查询条件
   * @returns 教师统计数据
   */
  async getTeacherStatistics(
    queryDto: TeacherStatisticsQueryDTO
  ): Promise<ITeacherStatistics> {
    const {
      sso_teacher_id,
      sso_school_code,
      month,
      start_month,
      end_month,
      include_distribution,
      include_keywords,
      include_trend,
    } = queryDto;

    // 构建查询条件
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id };

    if (sso_school_code) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = sso_school_code;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    } else if (start_month && end_month) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = start_month;
      replacements.end_month = end_month;
    }

    // 基础统计查询
    const basicStatsQuery = `
      SELECT
        a.sso_teacher_id,
        a.sso_teacher_name,
        a.sso_teacher_subject,
        a.sso_teacher_department,
        COUNT(a.id) as total_evaluations,
        ROUND(AVG(a.rating), 2) as average_score
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY a.sso_teacher_id, a.sso_teacher_name, a.sso_teacher_subject, a.sso_teacher_department
    `;

    const [basicStats] = (await this.sequelize.query(basicStatsQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    if (!basicStats) {
      throw new CustomError('未找到该教师的统计数据');
    }

    const averageScore = parseFloat(basicStats.average_score) || 0;
    const result: ITeacherStatistics = {
      sso_teacher_id: basicStats.sso_teacher_id,
      sso_teacher_name: basicStats.sso_teacher_name,
      sso_teacher_subject: basicStats.sso_teacher_subject,
      sso_teacher_department: basicStats.sso_teacher_department,
      month: month,
      total_evaluations: parseInt(basicStats.total_evaluations) || 0,
      average_score: averageScore,
      recommendation_rate: this.calculateRecommendationRate(averageScore),
      detailed_scores: {
        teaching_quality: averageScore,
        teaching_attitude: averageScore,
        classroom_management: averageScore,
        communication: averageScore,
        professional_knowledge: averageScore,
      },
    };

    // 如果需要评分分布
    if (include_distribution) {
      result.score_distribution = await this.getScoreDistribution(
        sso_teacher_id,
        sso_school_code,
        month
      );
    }

    // 如果需要关键词云
    if (include_keywords) {
      result.keyword_cloud = await this.getKeywordCloud(
        sso_teacher_id,
        sso_school_code,
        month
      );
    }

    // 如果需要趋势数据
    if (include_trend && start_month && end_month) {
      result.evaluation_trend = await this.getTeacherTrend(
        sso_teacher_id,
        sso_school_code,
        start_month,
        end_month
      );
    }

    return result;
  }

  /**
   * 获取教师排名
   * @param queryDto 查询条件
   * @returns 教师排名列表
   */
  async getTeacherRanking(
    queryDto: TeacherRankingQueryDTO
  ): Promise<ITeacherRankingResponse> {
    const {
      sso_school_code,
      month,
      subject,
      department,
      page = 1,
      limit = 20,
      sort_by = 'average_score',
      sort_order = 'DESC',
    } = queryDto;

    // 构建查询条件
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code };

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    if (subject) {
      whereClause += ' AND a.sso_teacher_subject = :subject';
      replacements.subject = subject;
    }

    if (department) {
      whereClause += ' AND a.sso_teacher_department = :department';
      replacements.department = department;
    }

    // 排序字段映射
    const sortFieldMap = {
      average_score: 'average_score',
      evaluation_count: 'evaluation_count',
      recommendation_rate: 'recommendation_rate',
    };

    const sortField = sortFieldMap[sort_by] || 'average_score';

    const rankingQuery = `
      SELECT
        a.sso_teacher_id,
        a.sso_teacher_name,
        a.sso_teacher_subject,
        a.sso_teacher_department,
        COUNT(a.id) as evaluation_count,
        ROUND(AVG(a.rating), 2) as average_score
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY a.sso_teacher_id, a.sso_teacher_name, a.sso_teacher_subject, a.sso_teacher_department
      ORDER BY ${sortField} ${sort_order}
      LIMIT :limit OFFSET :offset
    `;

    const offset = (page - 1) * limit;
    replacements.limit = limit;
    replacements.offset = offset;

    const rankings = (await this.sequelize.query(rankingQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    // 获取总数
    const countQuery = `
      SELECT COUNT(DISTINCT a.sso_teacher_id) as total
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
    `;

    const [countResult] = (await this.sequelize.query(countQuery, {
      type: QueryTypes.SELECT,
      replacements: { sso_school_code, month, subject, department },
    })) as any[];

    const list = rankings.map((item, index) => {
      const averageScore = parseFloat(item.average_score) || 0;
      return {
        sso_teacher_id: item.sso_teacher_id,
        sso_teacher_name: item.sso_teacher_name,
        sso_teacher_subject: item.sso_teacher_subject,
        sso_teacher_department: item.sso_teacher_department,
        average_score: averageScore,
        evaluation_count: parseInt(item.evaluation_count) || 0,
        recommendation_rate: this.calculateRecommendationRate(averageScore),
        rank: offset + index + 1,
      };
    });

    return {
      list,
      total: parseInt(countResult.total) || 0,
    };
  }

  /**
   * 获取响应趋势数据
   * @param ssoSchoolId 学校ID
   * @param startMonth 开始月份
   * @param endMonth 结束月份
   * @returns 趋势数据
   */
  async getResponseTrend(
    ssoSchoolId: string,
    startMonth: string,
    endMonth: string
  ): Promise<any[]> {
    const trendQuery = `
      SELECT
        r.month,
        COUNT(DISTINCT r.id) as total_responses,
        COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.id END) as completed_responses,
        ROUND(AVG(r.school_rating), 2) as avg_school_score,
        ROUND(AVG(r.total_average_score), 2) as avg_teacher_score
      FROM responses r
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE q.sso_school_code = :sso_school_code
        AND r.month BETWEEN :start_month AND :end_month
      GROUP BY r.month
      ORDER BY r.month
    `;

    const trendData = (await this.sequelize.query(trendQuery, {
      type: QueryTypes.SELECT,
      replacements: {
        sso_school_code: ssoSchoolId,
        start_month: startMonth,
        end_month: endMonth,
      },
    })) as any[];

    return trendData.map(item => ({
      month: item.month,
      total_responses: parseInt(item.total_responses) || 0,
      completed_responses: parseInt(item.completed_responses) || 0,
      completion_rate:
        item.total_responses > 0
          ? Math.round(
              (item.completed_responses / item.total_responses) * 100 * 100
            ) / 100
          : 0,
      avg_school_score: parseFloat(item.avg_school_score) || 0,
      avg_teacher_score: parseFloat(item.avg_teacher_score) || 0,
    }));
  }

  /**
   * 获取教师趋势数据
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param startMonth 开始月份
   * @param endMonth 结束月份
   * @returns 教师趋势数据
   */
  async getTeacherTrend(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    startMonth?: string,
    endMonth?: string
  ): Promise<any[]> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (startMonth && endMonth) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = startMonth;
      replacements.end_month = endMonth;
    }

    const trendQuery = `
      SELECT
        r.month,
        COUNT(a.id) as evaluation_count,
        ROUND(AVG(a.rating), 2) as average_score
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY r.month
      ORDER BY r.month
    `;

    const trendData = (await this.sequelize.query(trendQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    return trendData.map(item => ({
      month: item.month,
      evaluation_count: parseInt(item.evaluation_count) || 0,
      average_score: parseFloat(item.average_score) || 0,
    }));
  }

  /**
   * 获取教师评分分布
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param month 月份
   * @returns 评分分布
   */
  async getScoreDistribution(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    month?: string
  ): Promise<any> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 先获取总数
    const totalCountQuery = `
      SELECT COUNT(*) as total_count
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
    `;

    const [totalResult] = (await this.sequelize.query(totalCountQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    const totalCount = parseInt(totalResult?.total_count) || 0;

    if (totalCount === 0) {
      return [];
    }

    const distributionQuery = `
      SELECT
        CASE
          WHEN a.rating >= 90 THEN '90-100'
          WHEN a.rating >= 80 THEN '80-89'
          WHEN a.rating >= 70 THEN '70-79'
          WHEN a.rating >= 60 THEN '60-69'
          ELSE '60以下'
        END as score_range,
        COUNT(*) as count
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY score_range
      ORDER BY score_range DESC
    `;

    const distribution = (await this.sequelize.query(distributionQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    return distribution.map(item => ({
      score_range: item.score_range,
      count: parseInt(item.count) || 0,
      percentage:
        Math.round((parseInt(item.count) / totalCount) * 100 * 100) / 100,
    }));
  }

  /**
   * 获取关键词云数据
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param month 月份
   * @returns 关键词云数据
   */
  async getKeywordCloud(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    month?: string
  ): Promise<any[]> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 获取所有评价描述
    const keywordQuery = `
      SELECT
        a.description
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
        AND a.description IS NOT NULL
    `;

    const keywordData = (await this.sequelize.query(keywordQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    // 简单的关键词提取和统计
    const keywordCount = new Map<string, number>();

    keywordData.forEach(item => {
      // 处理文本描述（简单的关键词提取）
      if (item.description && typeof item.description === 'string') {
        // 简单的中文关键词提取（这里可以集成更复杂的NLP库）
        const keywords = this.extractKeywords(item.description);
        keywords.forEach(keyword => {
          keywordCount.set(keyword, (keywordCount.get(keyword) || 0) + 1);
        });
      }
    });

    // 转换为数组并按频次排序
    const keywordArray = Array.from(keywordCount.entries())
      .map(([word, count]) => ({ word, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 50); // 取前50个关键词

    // 计算权重（相对于最高频次的比例）
    const maxCount = keywordArray.length > 0 ? keywordArray[0].count : 1;
    return keywordArray.map(item => ({
      word: item.word,
      count: item.count,
      weight: Math.round((item.count / maxCount) * 100) / 100,
    }));
  }

  /**
   * 计算推荐率
   * @param averageScore 平均分
   * @param threshold 推荐阈值，默认80分
   * @returns 推荐率（百分比）
   */
  private calculateRecommendationRate(
    averageScore: number,
    threshold = 80
  ): number {
    if (averageScore >= threshold) {
      return 100;
    }
    return Math.round((averageScore / threshold) * 100 * 100) / 100;
  }

  /**
   * 简单的关键词提取方法
   * @param text 文本
   * @returns 关键词数组
   */
  private extractKeywords(text: string): string[] {
    // 这是一个简化的关键词提取方法
    // 在实际项目中，建议使用专业的中文分词和关键词提取库

    // 常见的正面评价词汇
    const positiveKeywords = [
      '认真',
      '负责',
      '专业',
      '耐心',
      '细心',
      '优秀',
      '棒',
      '好',
      '满意',
      '喜欢',
      '推荐',
      '赞',
      '不错',
      '很好',
      '非常好',
      '教学',
      '方法',
      '互动',
      '生动',
      '有趣',
      '清楚',
      '明白',
      '理解',
      '进步',
      '提高',
    ];

    // 常见的负面评价词汇
    const negativeKeywords = [
      '不好',
      '差',
      '不满意',
      '不喜欢',
      '问题',
      '困难',
      '不清楚',
      '不明白',
      '改进',
      '提升',
      '加强',
      '注意',
      '希望',
      '建议',
      '需要',
      '应该',
    ];

    // 教学相关词汇
    const teachingKeywords = [
      '教学',
      '课堂',
      '作业',
      '考试',
      '学习',
      '知识',
      '技能',
      '能力',
      '方法',
      '技巧',
      '经验',
      '指导',
      '帮助',
      '支持',
      '鼓励',
      '激励',
    ];

    const allKeywords = [
      ...positiveKeywords,
      ...negativeKeywords,
      ...teachingKeywords,
    ];
    const foundKeywords: string[] = [];

    allKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        foundKeywords.push(keyword);
      }
    });

    return foundKeywords;
  }

  /**
   * 获取趋势分析数据
   * @param queryDto 查询条件
   * @returns 趋势分析数据
   */
  async getTrendAnalysis(queryDto: TrendAnalysisQueryDTO): Promise<any> {
    const {
      sso_school_code,
      start_month,
      end_month,
      sso_teacher_id,
      analysis_type,
    } = queryDto;

    if (analysis_type === 'teacher' && sso_teacher_id) {
      return await this.getTeacherTrend(
        sso_teacher_id,
        sso_school_code,
        start_month,
        end_month
      );
    } else {
      return await this.getResponseTrend(
        sso_school_code,
        start_month,
        end_month
      );
    }
  }

  /**
   * 获取未填写学生名单统计（分页版本）
   * @param ssoSchoolCode 学校编码
   * @param questionnaireId 问卷ID，可选
   * @param month 月份，可选
   * @param page 页码，默认1
   * @param pageSize 每页数量，默认20
   * @param gradeCode 年级编码筛选，可选
   * @param classCode 班级编码筛选，可选
   * @returns 分页的未填写学生统计信息
   */
  async getIncompleteStudentsWithPagination(
    ssoSchoolCode: string,
    questionnaireId?: number,
    month?: string,
    page = 1,
    pageSize = 20,
    gradeCode?: string,
    classCode?: string
  ): Promise<IIncompleteStudentsResponse> {
    // 获取完整的统计数据
    const summary = await this.getIncompleteStudentsSummary(
      ssoSchoolCode,
      questionnaireId,
      month
    );

    // 应用筛选条件
    let filteredClasses = summary.by_class;

    if (gradeCode) {
      filteredClasses = filteredClasses.filter(
        cls => cls.grade_code === gradeCode
      );
    }

    if (classCode) {
      filteredClasses = filteredClasses.filter(
        cls => cls.class_code === classCode
      );
    }

    // 计算分页
    const total = filteredClasses.length;
    const totalPages = Math.ceil(total / pageSize);
    const offset = (page - 1) * pageSize;
    const paginatedClasses = filteredClasses.slice(offset, offset + pageSize);

    return {
      summary,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
      },
      classes: paginatedClasses,
    };
  }

  /**
   * 从学生信息中提取年级编号和班级编号
   * 与ResponseService中的逻辑保持一致
   * @param studentInfo 学生信息
   * @returns 年级编号和班级编号
   */
  private extractGradeAndClassCodes(studentInfo: any): {
    gradeCode: string | null;
    classCode: string | null;
  } {
    let gradeCode: string | null = null;
    let classCode: string | null = null;

    // 智能提取班级和年级信息（与ResponseService保持一致）
    const classAndGradeInfo = this.extractClassAndGradeInfo(studentInfo);

    console.log('统计服务提取年级班级编号', {
      student_code: studentInfo.code,
      student_name: studentInfo.name,
      original_grade: classAndGradeInfo.gradeName,
      original_class: classAndGradeInfo.className,
    });

    // 从年级信息中提取年级编号
    if (classAndGradeInfo.gradeName) {
      gradeCode = this.extractNumberFromText(classAndGradeInfo.gradeName);
    }

    // 从班级信息中提取班级编号
    if (classAndGradeInfo.className) {
      classCode = this.extractNumberFromText(classAndGradeInfo.className);
    }

    return { gradeCode, classCode };
  }

  /**
   * 智能提取班级和年级信息（与ResponseService保持一致）
   * @param studentInfo 原始学生信息
   * @returns 班级和年级信息
   */
  private extractClassAndGradeInfo(studentInfo: any): {
    className: string;
    gradeName: string;
  } {
    let className = '';
    let gradeName = '';

    // 方式1: 从classes数组中获取（优先级最高）
    if (
      studentInfo.classes &&
      Array.isArray(studentInfo.classes) &&
      studentInfo.classes.length > 0
    ) {
      const firstClass = studentInfo.classes[0];
      className = firstClass.name || '';
      gradeName = firstClass.grade_name || '';
      return { className, gradeName };
    }

    // 方式2: 从class对象中获取
    if (studentInfo.class && typeof studentInfo.class === 'object') {
      className = studentInfo.class.name || '';
      if (studentInfo.class.grade) {
        gradeName =
          studentInfo.class.grade.name || studentInfo.class.grade || '';
      }
      return { className, gradeName };
    }

    // 方式3: 从简单字符串字段获取
    if (typeof studentInfo.class === 'string') {
      className = studentInfo.class;
    }
    if (typeof studentInfo.grade === 'string') {
      gradeName = studentInfo.grade;
    }

    return { className, gradeName };
  }

  /**
   * 从文本中提取数字编号（与ResponseService保持一致）
   * @param text 原始文本
   * @param type 类型（用于日志）
   * @returns 提取的数字编号
   */
  private extractNumberFromText(text: string): string | null {
    if (!text || typeof text !== 'string') {
      return null;
    }

    // 1. 尝试提取阿拉伯数字（优先级最高）
    const arabicMatch = text.match(/(\d+)/);
    if (arabicMatch) {
      return arabicMatch[1];
    }

    // 2. 特殊情况处理（优先级高于普通中文数字）
    const specialCases: { [key: string]: string } = {
      // 初中年级
      初一: '7',
      初二: '8',
      初三: '9',
      初中一年级: '7',
      初中二年级: '8',
      初中三年级: '9',
      初中1年级: '7',
      初中2年级: '8',
      初中3年级: '9',

      // 高中年级
      高一: '10',
      高二: '11',
      高三: '12',
      高中一年级: '10',
      高中二年级: '11',
      高中三年级: '12',
      高中1年级: '10',
      高中2年级: '11',
      高中3年级: '12',

      // 小学年级
      小一: '1',
      小二: '2',
      小三: '3',
      小四: '4',
      小五: '5',
      小六: '6',
      小学一年级: '1',
      小学二年级: '2',
      小学三年级: '3',
      小学四年级: '4',
      小学五年级: '5',
      小学六年级: '6',
      小学1年级: '1',
      小学2年级: '2',
      小学3年级: '3',
      小学4年级: '4',
      小学5年级: '5',
      小学6年级: '6',

      // 幼儿园
      幼小班: '1',
      幼中班: '2',
      幼大班: '3',
    };

    for (const [special, number] of Object.entries(specialCases)) {
      if (text.includes(special)) {
        return number;
      }
    }

    // 3. 尝试转换中文数字（包括复合数字）
    // 先处理复合数字（十一、十二等）
    const complexChineseNumbers: { [key: string]: string } = {
      十一: '11',
      十二: '12',
      十三: '13',
      十四: '14',
      十五: '15',
      十六: '16',
      十七: '17',
      十八: '18',
      十九: '19',
      二十: '20',
    };

    for (const [chinese, number] of Object.entries(complexChineseNumbers)) {
      if (text.includes(chinese)) {
        return number;
      }
    }

    // 再处理简单中文数字
    const simpleChineseNumbers: { [key: string]: string } = {
      一: '1',
      二: '2',
      三: '3',
      四: '4',
      五: '5',
      六: '6',
      七: '7',
      八: '8',
      九: '9',
      十: '10',
    };

    for (const [chinese, number] of Object.entries(simpleChineseNumbers)) {
      if (text.includes(chinese)) {
        return number;
      }
    }

    // 4. 尝试提取英文序号
    const englishMatch = text.match(/([A-Za-z]+)/);
    if (englishMatch) {
      const letter = englishMatch[1].toUpperCase();
      // 将字母转换为数字 (A=1, B=2, ...)
      if (letter.length === 1 && letter >= 'A' && letter <= 'Z') {
        const number = (
          letter.charCodeAt(0) -
          'A'.charCodeAt(0) +
          1
        ).toString();
        return number;
      }
    }

    return null;
  }

  /**
   * 修复学生编码不匹配问题
   * @param ssoSchoolCode 学校编码
   * @param month 月份
   * @param dryRun 是否只是预览，不实际修改数据
   */
  async fixStudentCodeMismatch(
    ssoSchoolCode: string,
    month: string,
    dryRun = true
  ): Promise<any> {
    console.log('=== 开始修复学生编码不匹配问题 ===');

    // 1. 获取SSO学生信息
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: ssoSchoolCode,
    });

    // 创建学生姓名到编码的映射
    const studentNameToCodeMap = new Map<string, string>();
    allStudents.forEach(student => {
      studentNameToCodeMap.set(student.name, student.code);
    });

    console.log('SSO学生总数:', allStudents.length);

    // 2. 查询响应表中的学生记录
    const responseQuery = `
      SELECT DISTINCT
        r.sso_student_code,
        r.sso_student_name,
        COUNT(*) as response_count
      FROM responses r
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE q.sso_school_code = :sso_school_code
        AND r.month = :month
      GROUP BY r.sso_student_code, r.sso_student_name
      ORDER BY r.sso_student_name
    `;

    const responseStudents = await this.sequelize.query(responseQuery, {
      type: QueryTypes.SELECT,
      replacements: {
        sso_school_code: ssoSchoolCode,
        month: month,
      },
    }) as any[];

    console.log('响应表中的学生记录数:', responseStudents.length);

    // 3. 检查不匹配的记录
    const mismatchedRecords = [];
    const correctRecords = [];

    for (const responseStudent of responseStudents) {
      const correctCode = studentNameToCodeMap.get(responseStudent.sso_student_name);

      if (!correctCode) {
        // SSO中找不到这个学生
        mismatchedRecords.push({
          type: 'student_not_found_in_sso',
          response_code: responseStudent.sso_student_code,
          response_name: responseStudent.sso_student_name,
          correct_code: null,
          response_count: responseStudent.response_count,
        });
      } else if (correctCode !== responseStudent.sso_student_code) {
        // 编码不匹配
        mismatchedRecords.push({
          type: 'code_mismatch',
          response_code: responseStudent.sso_student_code,
          response_name: responseStudent.sso_student_name,
          correct_code: correctCode,
          response_count: responseStudent.response_count,
        });
      } else {
        // 编码正确
        correctRecords.push({
          response_code: responseStudent.sso_student_code,
          response_name: responseStudent.sso_student_name,
          response_count: responseStudent.response_count,
        });
      }
    }

    console.log('匹配结果统计:', {
      total_response_students: responseStudents.length,
      correct_records: correctRecords.length,
      mismatched_records: mismatchedRecords.length,
    });

    // 4. 如果不是预览模式，执行修复
    const fixResults = [];
    if (!dryRun && mismatchedRecords.length > 0) {
      console.log('开始修复数据...');

      for (const record of mismatchedRecords) {
        if (record.type === 'code_mismatch' && record.correct_code) {
          try {
            const updateResult = await this.sequelize.query(
              `UPDATE responses r
               JOIN questionnaires q ON r.questionnaire_id = q.id
               SET r.sso_student_code = :correct_code
               WHERE q.sso_school_code = :sso_school_code
                 AND r.month = :month
                 AND r.sso_student_name = :student_name
                 AND r.sso_student_code = :old_code`,
              {
                type: QueryTypes.UPDATE,
                replacements: {
                  correct_code: record.correct_code,
                  sso_school_code: ssoSchoolCode,
                  month: month,
                  student_name: record.response_name,
                  old_code: record.response_code,
                },
              }
            );

            fixResults.push({
              student_name: record.response_name,
              old_code: record.response_code,
              new_code: record.correct_code,
              updated_count: updateResult[1], // 受影响的行数
              status: 'success',
            });

            console.log(`已修复学生 ${record.response_name}: ${record.response_code} -> ${record.correct_code}`);
          } catch (error) {
            fixResults.push({
              student_name: record.response_name,
              old_code: record.response_code,
              new_code: record.correct_code,
              error: error.message,
              status: 'failed',
            });

            console.error(`修复学生 ${record.response_name} 失败:`, error.message);
          }
        }
      }
    }

    return {
      summary: {
        sso_students_count: allStudents.length,
        response_students_count: responseStudents.length,
        correct_records_count: correctRecords.length,
        mismatched_records_count: mismatchedRecords.length,
      },
      correct_records: correctRecords,
      mismatched_records: mismatchedRecords,
      fix_results: fixResults,
      dry_run: dryRun,
    };
  }

  /**
   * 调试学生匹配问题
   * @param ssoSchoolCode 学校编码
   * @param studentName 学生姓名
   * @param month 月份
   */
  async debugStudentMatching(
    ssoSchoolCode: string,
    studentName: string,
    month: string
  ): Promise<any> {
    console.log('=== 开始调试学生匹配问题 ===');

    // 1. 获取SSO学生信息
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: ssoSchoolCode,
    });

    const targetStudent = allStudents.find(s => s.name === studentName);
    console.log('SSO学生信息:', {
      found: !!targetStudent,
      student_info: targetStudent ? {
        code: targetStudent.code,
        name: targetStudent.name,
        classes: targetStudent.classes,
      } : null,
    });

    // 2. 查询问卷响应记录
    const responseQuery = `
      SELECT r.*, q.sso_school_code, q.title
      FROM responses r
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE q.sso_school_code = :sso_school_code
        AND r.month = :month
        AND r.sso_student_name = :student_name
    `;

    const responses = await this.sequelize.query(responseQuery, {
      type: QueryTypes.SELECT,
      replacements: {
        sso_school_code: ssoSchoolCode,
        month: month,
        student_name: studentName,
      },
    });

    console.log('问卷响应记录:', {
      found_responses: responses.length,
      responses: responses,
    });

    // 3. 检查编码匹配
    if (targetStudent && responses.length > 0) {
      const response = responses[0] as any;
      console.log('编码匹配检查:', {
        sso_student_code: targetStudent.code,
        response_student_code: response.sso_student_code,
        codes_match: targetStudent.code === response.sso_student_code,
        sso_code_type: typeof targetStudent.code,
        response_code_type: typeof response.sso_student_code,
      });
    }

    return {
      sso_student: targetStudent,
      responses: responses,
    };
  }

  /**
   * 获取未填写学生名单统计
   * @param ssoSchoolCode 学校编码
   * @param questionnaireId 问卷ID，可选
   * @param month 月份，可选
   * @returns 未填写学生统计信息
   */
  async getIncompleteStudentsSummary(
    ssoSchoolCode: string,
    questionnaireId?: number,
    month?: string
  ): Promise<IIncompleteStudentsSummary> {
    // 获取学校所有学生
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: ssoSchoolCode,
    });

    // 构建查询条件获取已填写问卷的学生
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code: ssoSchoolCode };

    if (questionnaireId) {
      whereClause += ' AND r.questionnaire_id = :questionnaire_id';
      replacements.questionnaire_id = questionnaireId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 查询已完成问卷的学生编码
    const completedStudentsQuery = `
      SELECT DISTINCT r.sso_student_code, r.sso_student_name, r.sso_student_class, r.sso_student_grade, r.grade_code, r.class_code
      FROM responses r
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause} AND r.is_completed = 1
    `;

    const completedStudents = (await this.sequelize.query(
      completedStudentsQuery,
      {
        type: QueryTypes.SELECT,
        replacements,
      }
    )) as any[];

    console.log('=== 统计服务调试信息 ===');
    console.log('查询条件:', {
      school_code: ssoSchoolCode,
      questionnaire_id: questionnaireId,
      month: month,
      query: completedStudentsQuery,
      replacements: replacements,
    });
    console.log('查询结果:', {
      completed_students_count: completedStudents.length,
      completed_students_sample: completedStudents.slice(0, 5),
    });

    // 专门查找张妍美的记录
    const zhangYanmeiRecord = completedStudents.find(s => s.sso_student_name === '张妍美');
    console.log('张妍美记录查找结果:', {
      found: !!zhangYanmeiRecord,
      record: zhangYanmeiRecord,
    });

    // 如果没找到，尝试模糊匹配
    if (!zhangYanmeiRecord) {
      const similarRecords = completedStudents.filter(s =>
        s.sso_student_name && s.sso_student_name.includes('张')
      );
      console.log('包含"张"字的记录:', similarRecords);
    }

    const completedStudentCodes = new Set(
      completedStudents.map(item => item.sso_student_code)
    );

    console.log('=== SSO学生信息调试 ===');
    console.log('学校编码:', ssoSchoolCode);
    console.log('总学生数:', allStudents.length);

    // 查找张妍美学生
    const zhangYanmeiStudent = allStudents.find(s => s.name === '张妍美');
    console.log('张妍美学生信息:', {
      found: !!zhangYanmeiStudent,
      student_info: zhangYanmeiStudent ? {
        code: zhangYanmeiStudent.code,
        name: zhangYanmeiStudent.name,
        classes: zhangYanmeiStudent.classes,
      } : null,
    });

    // 如果没找到，查找包含"张"字的学生
    if (!zhangYanmeiStudent) {
      const zhangStudents = allStudents.filter(s => s.name && s.name.includes('张'));
      console.log('包含"张"字的学生:', zhangStudents.map(s => ({
        code: s.code,
        name: s.name,
      })));
    }

    console.log('前3个学生样本:', allStudents.slice(0, 3).map(s => ({
      code: s.code,
      name: s.name,
      classes: s.classes?.map(c => ({
        name: c.name,
        grade_name: c.grade_name,
        code: c.code,
        grade_code: c.grade_code,
      })),
    })));

    // 找出未填写的学生
    const incompleteStudents: IIncompleteStudent[] = allStudents
      .filter(student => {
        const isCompleted = completedStudentCodes.has(student.code);
        if (student.name === '张妍美') {
          console.log('统计服务 - 检查张妍美学生', {
            student_code: student.code,
            student_name: student.name,
            is_in_completed_set: isCompleted,
            completed_student_codes_array: Array.from(completedStudentCodes),
          });
        }
        return !isCompleted;
      })
      .map(student => {
        // 使用与ResponseService相同的年级班级编码提取逻辑
        const extractedCodes = this.extractGradeAndClassCodes(student);

        return {
          sso_student_code: student.code,
          sso_student_name: student.name,
          grade_code:
            extractedCodes.gradeCode || student.classes[0]?.grade_code || '',
          grade_name: student.classes[0]?.grade_name || '',
          class_code:
            extractedCodes.classCode || student.classes[0]?.code || '',
          class_name: student.classes[0]?.name || '',
        };
      });

    // 按年级分组统计
    const gradeStats = new Map<
      string,
      {
        grade_code: string;
        grade_name: string;
        incomplete_count: number;
        total_students: number;
      }
    >();

    // 按班级分组统计
    const classStats = new Map<string, IIncompleteStudentsByClass>();

    // 统计所有学生（按年级和班级）
    allStudents.forEach(student => {
      // 使用与ResponseService相同的年级班级编码提取逻辑
      const extractedCodes = this.extractGradeAndClassCodes(student);

      const gradeCode =
        extractedCodes.gradeCode || student.classes[0]?.grade_code || '';
      const gradeName = student.classes[0]?.grade_name || '';
      const classCode =
        extractedCodes.classCode || student.classes[0]?.code || '';
      const className = student.classes[0]?.name || '';

      // 年级统计
      if (!gradeStats.has(gradeCode)) {
        gradeStats.set(gradeCode, {
          grade_code: gradeCode,
          grade_name: gradeName,
          incomplete_count: 0,
          total_students: 0,
        });
      }
      gradeStats.get(gradeCode)!.total_students++;

      // 班级统计
      const classKey = `${gradeCode}-${classCode}`;
      if (!classStats.has(classKey)) {
        classStats.set(classKey, {
          grade_code: gradeCode,
          grade_name: gradeName,
          class_code: classCode,
          class_name: className,
          incomplete_count: 0,
          total_students: 0,
          completion_rate: 0,
          students: [],
        });
      }
      classStats.get(classKey)!.total_students++;
    });

    // 统计未填写学生
    incompleteStudents.forEach(student => {
      // 年级统计
      if (gradeStats.has(student.grade_code)) {
        gradeStats.get(student.grade_code)!.incomplete_count++;
      }

      // 班级统计
      const classKey = `${student.grade_code}-${student.class_code}`;
      if (classStats.has(classKey)) {
        const classStat = classStats.get(classKey)!;
        classStat.incomplete_count++;
        classStat.students.push(student);
      }
    });

    // 计算完成率并转换为数组
    const byGrade = Array.from(gradeStats.values()).map(grade => ({
      ...grade,
      completion_rate:
        grade.total_students > 0
          ? Math.round(
              ((grade.total_students - grade.incomplete_count) /
                grade.total_students) *
                100 *
                100
            ) / 100
          : 0,
    }));

    const byClass = Array.from(classStats.values()).map(classItem => ({
      ...classItem,
      completion_rate:
        classItem.total_students > 0
          ? Math.round(
              ((classItem.total_students - classItem.incomplete_count) /
                classItem.total_students) *
                100 *
                100
            ) / 100
          : 0,
    }));

    return {
      total_incomplete: incompleteStudents.length,
      by_grade: byGrade,
      by_class: byClass,
    };
  }

  // ==================== 统计任务相关方法 ====================

  /**
   * 触发统计计算任务
   * @param questionnaireId 问卷ID
   * @param triggeredBy 触发用户
   * @param syncMode 是否同步模式（小数据量时可以同步返回）
   * @returns 统计记录
   */
  async triggerStatisticsCalculation(
    questionnaireId: number,
    triggeredBy = 'system'
  ): Promise<QuestionnaireStatistics> {
    // 1. 验证问卷是否存在
    const questionnaire = await this.questionnaireRepository.findByPk(
      questionnaireId
    );
    if (!questionnaire) {
      throw new CustomError('问卷不存在', 404);
    }

    // 2. 检查是否已有正在计算的任务
    const existingStats = await this.statisticsRepository.findOne({
      where: {
        questionnaire_id: questionnaireId,
        status: 'calculating',
      },
    });

    if (existingStats) {
      throw new CustomError('该问卷正在计算统计数据，请稍后再试', 409);
    }

    // 3. 创建或更新统计记录
    let statistics = await this.statisticsRepository.findOne({
      where: { questionnaire_id: questionnaireId },
    });

    if (!statistics) {
      statistics = await this.statisticsRepository.create({
        questionnaire_id: questionnaireId,
        sso_school_code: questionnaire.sso_school_code,
        month: questionnaire.month,
        status: 'calculating',
        triggered_by: triggeredBy,
        calculation_start_time: new Date(),
      });
    } else {
      await statistics.update({
        status: 'calculating',
        triggered_by: triggeredBy,
        calculation_start_time: new Date(),
        error_message: null,
      });
    }

    // 4. 异步执行统计计算
    this.executeStatisticsCalculation(statistics.id, questionnaireId).catch(
      error => {
        this.ctx.logger.error('统计计算异步执行失败', {
          statistics_id: statistics.id,
          questionnaire_id: questionnaireId,
          error: error.message,
        });
      }
    );

    return statistics;
  }

  /**
   * 获取统计状态
   * @param questionnaireId 问卷ID
   * @returns 统计状态信息
   */
  async getStatisticsStatus(
    questionnaireId: number
  ): Promise<QuestionnaireStatistics | null> {
    return await this.statisticsRepository.findOne({
      where: { questionnaire_id: questionnaireId },
      order: [['created_at', 'DESC']],
    });
  }

  /**
   * 获取缓存的统计数据
   * @param questionnaireId 问卷ID
   * @returns 缓存的统计数据
   */
  async getCachedStatistics(questionnaireId: number): Promise<any> {
    const statistics = await this.statisticsRepository.findOne({
      where: {
        questionnaire_id: questionnaireId,
        status: 'completed',
      },
      include: [
        {
          model: GradeStatistics,
          as: 'gradeStatistics',
        },
        {
          model: ClassStatistics,
          as: 'classStatistics',
        },
        {
          model: TeacherStatistics,
          as: 'teacherStatistics',
        },
      ],
      order: [['last_calculated_at', 'DESC']],
    });

    if (!statistics) {
      return null;
    }

    // 构建缓存的统计数据
    const cachedData = {
      statistics_id: statistics.id,
      questionnaire_id: statistics.questionnaire_id,
      sso_school_code: statistics.sso_school_code,
      month: statistics.month,
      total_students: statistics.total_students,
      submitted_count: statistics.submitted_count,
      incomplete_count: statistics.incomplete_count,
      completion_rate: statistics.completion_rate,
      school_average_score: statistics.school_average_score,
      teacher_average_score: statistics.teacher_average_score,
      total_teachers: statistics.total_teachers,
      last_calculated_at: statistics.last_calculated_at,
      calculation_duration: statistics.calculation_duration,

      // 关联数据
      grade_statistics: statistics.gradeStatistics || [],
      class_statistics: statistics.classStatistics || [],
      teacher_ranking: statistics.teacherStatistics || [],
    };

    return cachedData;
  }

  /**
   * 获取缓存的未填写学生列表
   * @param questionnaireId 问卷ID
   * @param queryDto 查询参数
   * @returns 未填写学生列表
   */
  async getCachedIncompleteStudents(
    questionnaireId: number,
    queryDto: Partial<IncompleteStudentsQueryDTO>
  ): Promise<any> {
    // 1. 获取统计状态
    const statistics = await this.getStatisticsStatus(questionnaireId);

    if (!statistics || statistics.status !== 'completed') {
      return {
        status: statistics?.status || 'not_started',
        list: [],
        total: 0,
        page: queryDto.page || 1,
        pageSize: queryDto.pageSize || 20,
        totalPages: 0,
      };
    }

    // 2. 构建查询条件
    const where: any = {
      statistics_id: statistics.id,
    };

    if (queryDto.grade_code) {
      where.grade_code = queryDto.grade_code;
    }

    if (queryDto.class_code) {
      where.class_code = queryDto.class_code;
    }

    // 3. 分页查询
    const page = queryDto.page || 1;
    const pageSize = queryDto.pageSize || 20;
    const offset = (page - 1) * pageSize;

    const { rows: list, count: total } =
      await this.incompleteStudentsCacheRepository.findAndCountAll({
        where,
        offset,
        limit: pageSize,
        order: [
          ['grade_code', 'ASC'],
          ['class_code', 'ASC'],
          ['sso_student_name', 'ASC'],
        ],
      });

    const totalPages = Math.ceil(total / pageSize);

    return {
      status: statistics.status,
      last_calculated_at: statistics.last_calculated_at,
      list: list.map(item => ({
        sso_student_code: item.sso_student_code,
        sso_student_name: item.sso_student_name,
        grade_code: item.grade_code,
        grade_name: item.grade_name,
        class_code: item.class_code,
        class_name: item.class_name,
        student_mobile: item.student_mobile,
        parent_contacts: item.parent_contacts
          ? JSON.parse(item.parent_contacts)
          : null,
        student_status: item.student_status,
      })),
      total,
      page,
      pageSize,
      totalPages,
    };
  }

  /**
   * 执行统计计算（私有方法）
   * @param statisticsId 统计记录ID
   * @param questionnaireId 问卷ID
   */
  private async executeStatisticsCalculation(
    statisticsId: number,
    questionnaireId: number
  ): Promise<void> {
    const startTime = Date.now();
    let transaction: Transaction | undefined;

    try {
      this.ctx.logger.info('开始执行统计计算', {
        statistics_id: statisticsId,
        questionnaire_id: questionnaireId,
      });

      // 开启事务
      transaction = await this.sequelize.transaction();

      // 获取统计记录
      const statistics = await this.statisticsRepository.findByPk(
        statisticsId,
        {
          transaction,
        }
      );

      if (!statistics) {
        throw new Error('统计记录不存在');
      }

      // 获取问卷信息
      const questionnaire = await this.questionnaireRepository.findByPk(
        questionnaireId,
        {
          transaction,
        }
      );

      if (!questionnaire) {
        throw new Error('问卷不存在');
      }

      // 执行统计计算（复用现有的统计逻辑）
      const calculationResult = await this.calculateDetailedStatistics(
        questionnaire.sso_school_code,
        questionnaireId
      );

      // 清理旧的未填写学生缓存
      await this.incompleteStudentsCacheRepository.destroy({
        where: { statistics_id: statisticsId },
        transaction,
      });

      // 保存未填写学生到缓存表
      if (
        calculationResult.incompleteStudents &&
        calculationResult.incompleteStudents.length > 0
      ) {
        const cacheData = calculationResult.incompleteStudents.map(
          (student: any) => ({
            statistics_id: statisticsId,
            sso_student_code: student.sso_student_code,
            sso_student_name: student.sso_student_name,
            grade_code: student.grade_code,
            grade_name: student.grade_name,
            class_code: student.class_code,
            class_name: student.class_name,
            student_mobile: student.student_mobile || null,
            parent_contacts: student.parent_contacts
              ? JSON.stringify(student.parent_contacts)
              : null,
            student_status: student.student_status || null,
          })
        );

        await this.incompleteStudentsCacheRepository.bulkCreate(cacheData, {
          transaction,
        });
      }

      // 更新统计记录
      const calculationDuration = Date.now() - startTime;
      await statistics.update(
        {
          status: 'completed',
          total_students: calculationResult.totalStudents,
          submitted_count: calculationResult.submittedCount,
          incomplete_count: calculationResult.incompleteCount,
          completion_rate: calculationResult.completionRate,
          school_average_score: calculationResult.schoolAverageScore,
          teacher_average_score: calculationResult.teacherAverageScore,
          total_teachers: calculationResult.totalTeachers,
          grade_statistics: JSON.stringify(calculationResult.gradeStatistics),
          class_statistics: JSON.stringify(calculationResult.classStatistics),
          teacher_ranking: JSON.stringify(calculationResult.teacherRanking),
          last_calculated_at: new Date(),
          calculation_duration: calculationDuration,
          error_message: null,
        },
        { transaction }
      );

      // 提交事务
      await transaction.commit();

      this.ctx.logger.info('统计计算完成', {
        statistics_id: statisticsId,
        questionnaire_id: questionnaireId,
        duration: calculationDuration,
        total_students: calculationResult.totalStudents,
        completion_rate: calculationResult.completionRate,
      });
    } catch (error) {
      // 回滚事务
      if (transaction) {
        await transaction.rollback();
      }

      const calculationDuration = Date.now() - startTime;

      // 更新统计记录为失败状态
      try {
        await this.statisticsRepository.update(
          {
            status: 'failed',
            calculation_duration: calculationDuration,
            error_message: error.message,
          },
          {
            where: { id: statisticsId },
          }
        );
      } catch (updateError) {
        this.ctx.logger.error('更新统计失败状态时出错', {
          statistics_id: statisticsId,
          error: updateError.message,
        });
      }

      this.ctx.logger.error('统计计算失败', {
        statistics_id: statisticsId,
        questionnaire_id: questionnaireId,
        duration: calculationDuration,
        error: error.message,
        stack: error.stack,
      });

      throw error;
    }
  }

  /**
   * 计算详细统计数据（私有方法）
   * @param schoolCode 学校编码
   * @param questionnaireId 问卷ID
   * @returns 详细统计结果
   */
  private async calculateDetailedStatistics(
    schoolCode: string,
    questionnaireId: number
  ): Promise<any> {
    // 1. 获取所有学生信息
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: schoolCode,
    });

    // 2. 获取已提交的响应
    const submittedResponses = await this.responseRepository.findAll({
      where: {
        questionnaire_id: questionnaireId,
      },
      include: ['answers'],
    });

    // 3. 计算基础统计
    const totalStudents = allStudents.length;
    const submittedCount = submittedResponses.length;
    const completedStudents = submittedCount; // 简化处理，认为提交即完成
    const incompleteCount = totalStudents - completedStudents;
    const completionRate =
      totalStudents > 0
        ? Math.round((completedStudents / totalStudents) * 100 * 100) / 100
        : 0;

    // 4. 计算平均分
    let schoolAverageScore = 0;
    let teacherAverageScore = 0;
    let totalTeachers = 0;

    if (submittedResponses.length > 0) {
      const schoolScores = submittedResponses
        .map(r => r.school_rating)
        .filter(score => score != null);

      if (schoolScores.length > 0) {
        schoolAverageScore =
          Math.round(
            (schoolScores.reduce((sum, score) => sum + score, 0) /
              schoolScores.length) *
              100
          ) / 100;
      }

      const teacherScores = submittedResponses
        .map(r => r.total_average_score)
        .filter(score => score != null);

      if (teacherScores.length > 0) {
        teacherAverageScore =
          Math.round(
            (teacherScores.reduce((sum, score) => sum + score, 0) /
              teacherScores.length) *
              100
          ) / 100;
      }

      // 统计教师数量
      const teacherIds = new Set();
      submittedResponses.forEach(response => {
        if (response.answers) {
          response.answers.forEach(answer => {
            if (answer.sso_teacher_id) {
              teacherIds.add(answer.sso_teacher_id);
            }
          });
        }
      });
      totalTeachers = teacherIds.size;
    }

    // 5. 计算年级和班级统计（简化版本，只返回汇总数据）
    const gradeClassStats = await this.calculateSimpleGradeClassStats(
      allStudents,
      submittedResponses
    );

    // 6. 计算教师排名（简化版本）
    const teacherRanking =
      this.calculateSimpleTeacherRanking(submittedResponses);

    return {
      totalStudents,
      submittedCount,
      completedStudents,
      incompleteCount,
      completionRate,
      schoolAverageScore,
      teacherAverageScore,
      totalTeachers,
      gradeStatistics: gradeClassStats.gradeStats,
      classStatistics: gradeClassStats.classStats,
      teacherRanking,
      incompleteStudents: [], // 将在缓存中单独处理
    };
  }

  /**
   * 计算简单教师排名（私有方法）
   * @param responses 响应列表
   * @returns 教师排名
   */
  private calculateSimpleTeacherRanking(responses: Response[]): any[] {
    const teacherStats = new Map();

    responses.forEach(response => {
      if (response.answers) {
        response.answers.forEach(answer => {
          if (answer.sso_teacher_id) {
            const teacherId = answer.sso_teacher_id;
            if (!teacherStats.has(teacherId)) {
              teacherStats.set(teacherId, {
                sso_teacher_id: teacherId,
                sso_teacher_name: answer.sso_teacher_name,
                sso_teacher_subject: answer.sso_teacher_subject,
                total_score: 0,
                evaluation_count: 0,
                average_score: 0,
              });
            }

            const stats = teacherStats.get(teacherId);
            stats.total_score += answer.rating || 0;
            stats.evaluation_count += 1;
          }
        });
      }
    });

    // 计算平均分并排序
    return Array.from(teacherStats.values())
      .map((teacher: any) => {
        teacher.average_score =
          teacher.evaluation_count > 0
            ? Math.round(
                (teacher.total_score / teacher.evaluation_count) * 100
              ) / 100
            : 0;
        return teacher;
      })
      .sort((a: any, b: any) => b.average_score - a.average_score);
  }

  /**
   * 计算简化的年级班级统计（私有方法）
   * @param allStudents 所有学生列表
   * @param submittedResponses 已提交的响应
   * @returns 年级班级统计汇总
   */
  private async calculateSimpleGradeClassStats(
    allStudents: any[],
    submittedResponses: any[]
  ): Promise<{ gradeStats: any[]; classStats: any[] }> {
    // 创建已提交学生的映射
    const submittedStudentIds = new Set(
      submittedResponses.map(r => r.sso_student_id)
    );

    // 按年级和班级分组统计
    const gradeMap = new Map();
    const classMap = new Map();

    allStudents.forEach(student => {
      // 提取年级班级编码（简化版本）
      const gradeCode = this.simpleExtractGradeCode(student.grade || '');
      const classCode = this.simpleExtractClassCode(student.class || '');

      const gradeKey = `${gradeCode}`;
      const classKey = `${gradeCode}-${classCode}`;

      // 年级统计
      if (!gradeMap.has(gradeKey)) {
        gradeMap.set(gradeKey, {
          grade_code: gradeCode,
          grade_name: student.grade || `${gradeCode}年级`,
          total_students: 0,
          submitted_count: 0,
        });
      }
      const gradeStats = gradeMap.get(gradeKey);
      gradeStats.total_students++;
      if (submittedStudentIds.has(student.student_code)) {
        gradeStats.submitted_count++;
      }

      // 班级统计
      if (!classMap.has(classKey)) {
        classMap.set(classKey, {
          grade_code: gradeCode,
          grade_name: student.grade || `${gradeCode}年级`,
          class_code: classCode,
          class_name: student.class || `${classCode}班`,
          total_students: 0,
          submitted_count: 0,
        });
      }
      const classStats = classMap.get(classKey);
      classStats.total_students++;
      if (submittedStudentIds.has(student.student_code)) {
        classStats.submitted_count++;
      }
    });

    // 计算完成率
    const gradeStats = Array.from(gradeMap.values()).map((stats: any) => ({
      ...stats,
      completion_rate:
        stats.total_students > 0
          ? Math.round(
              (stats.submitted_count / stats.total_students) * 100 * 100
            ) / 100
          : 0,
    }));

    const classStats = Array.from(classMap.values()).map((stats: any) => ({
      ...stats,
      completion_rate:
        stats.total_students > 0
          ? Math.round(
              (stats.submitted_count / stats.total_students) * 100 * 100
            ) / 100
          : 0,
    }));

    return {
      gradeStats: gradeStats.sort(
        (a, b) => parseInt(a.grade_code) - parseInt(b.grade_code)
      ),
      classStats: classStats.sort((a, b) => {
        const gradeDiff = parseInt(a.grade_code) - parseInt(b.grade_code);
        return gradeDiff !== 0
          ? gradeDiff
          : parseInt(a.class_code) - parseInt(b.class_code);
      }),
    };
  }

  /**
   * 简化的年级编码提取（私有方法）
   * @param gradeText 年级文本
   * @returns 年级编码
   */
  private simpleExtractGradeCode(gradeText: string): string {
    // 提取数字
    const match = gradeText.match(/(\d+)/);
    return match ? match[1] : '0';
  }

  /**
   * 简化的班级编码提取（私有方法）
   * @param classText 班级文本
   * @returns 班级编码
   */
  private simpleExtractClassCode(classText: string): string {
    // 提取数字
    const match = classText.match(/(\d+)/);
    return match ? match[1] : '0';
  }
}
