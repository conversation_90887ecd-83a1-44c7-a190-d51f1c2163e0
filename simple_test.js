// 简单的测试脚本，直接输出分析结果

console.log('=== 张妍美学生匹配问题分析 ===');

console.log('问题描述：');
console.log('- 张妍美学生在问卷响应表中有记录');
console.log('- 但在统计未填写学生时被识别为未填写');

console.log('\n可能原因分析：');

console.log('1. 学生编码不匹配');
console.log('   - SSO系统返回的学生编码与响应表中的sso_student_code不一致');
console.log('   - 数据类型不匹配（字符串 vs 数字）');

console.log('2. 查询条件问题');
console.log('   - 统计查询的WHERE条件可能过滤掉了这条记录');
console.log('   - 问卷ID或月份条件不匹配');

console.log('3. 数据状态问题');
console.log('   - is_completed字段可能不是1');
console.log('   - 记录可能被软删除');

console.log('\n建议的调试步骤：');

console.log('1. 检查问卷响应表中张妍美的完整记录：');
console.log(`
SELECT 
  r.id,
  r.sso_student_code,
  r.sso_student_name,
  r.sso_student_class,
  r.sso_student_grade,
  r.grade_code,
  r.class_code,
  r.month,
  r.is_completed,
  r.questionnaire_id,
  q.sso_school_code,
  q.title
FROM responses r
JOIN questionnaires q ON r.questionnaire_id = q.id
WHERE r.sso_student_name = '张妍美'
  AND r.month = '2025-06'
ORDER BY r.created_at DESC;
`);

console.log('2. 检查SSO系统返回的张妍美学生信息：');
console.log('   - 学生编码是什么？');
console.log('   - 学生姓名是否完全匹配？');

console.log('3. 对比两个系统中的学生编码：');
console.log('   - 响应表中的sso_student_code');
console.log('   - SSO系统返回的student.code');

console.log('\n临时解决方案：');
console.log('如果确认是编码不匹配问题，可以：');
console.log('1. 修正响应表中的学生编码');
console.log('2. 或者在统计查询中同时使用姓名匹配作为备选方案');

console.log('\n请提供以下信息以进一步诊断：');
console.log('1. 张妍美在响应表中的sso_student_code值');
console.log('2. SSO系统返回的张妍美学生的code值');
console.log('3. 统计查询使用的具体参数（学校编码、问卷ID、月份）');
