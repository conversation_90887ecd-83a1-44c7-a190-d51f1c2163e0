-- 重新设计统计表结构，采用关系型设计替代JSON存储
-- 执行时间：2024-12-26

USE teacherEvaluation_questionService;

-- 1. 简化主统计表，只存储汇总数据
DROP TABLE IF EXISTS questionnaire_statistics_new;
CREATE TABLE questionnaire_statistics_new (
  id INT AUTO_INCREMENT PRIMARY KEY,
  questionnaire_id INT NOT NULL COMMENT '问卷ID',
  sso_school_code VARCHAR(50) NOT NULL COMMENT '学校编码',
  month VARCHAR(7) NOT NULL COMMENT '统计月份 YYYY-MM',
  
  -- 基础统计数据
  total_students INT DEFAULT 0 COMMENT '总学生数',
  submitted_count INT DEFAULT 0 COMMENT '已提交数量',
  incomplete_count INT DEFAULT 0 COMMENT '未提交数量',
  completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率(%)',
  
  -- 平均分数据
  school_average_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '学校平均分',
  teacher_average_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '教师平均分',
  total_teachers INT DEFAULT 0 COMMENT '被评价教师总数',
  
  -- 计算状态
  status ENUM('pending', 'calculating', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT '计算状态',
  triggered_by VARCHAR(100) DEFAULT 'system' COMMENT '触发用户',
  calculation_start_time DATETIME NULL COMMENT '计算开始时间',
  last_calculated_at DATETIME NULL COMMENT '最后计算时间',
  calculation_duration INT DEFAULT 0 COMMENT '计算耗时(毫秒)',
  error_message TEXT NULL COMMENT '错误信息',
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_questionnaire_statistics (questionnaire_id),
  INDEX idx_school_month (sso_school_code, month),
  INDEX idx_status (status),
  INDEX idx_calculated_at (last_calculated_at)
) COMMENT='问卷统计主表';

-- 2. 年级统计表
DROP TABLE IF EXISTS grade_statistics;
CREATE TABLE grade_statistics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  statistics_id INT NOT NULL COMMENT '主统计表ID',
  questionnaire_id INT NOT NULL COMMENT '问卷ID',
  sso_school_code VARCHAR(50) NOT NULL COMMENT '学校编码',
  
  grade_code VARCHAR(10) NOT NULL COMMENT '年级编码',
  grade_name VARCHAR(50) NOT NULL COMMENT '年级名称',
  total_students INT DEFAULT 0 COMMENT '年级总学生数',
  submitted_count INT DEFAULT 0 COMMENT '已提交数量',
  completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率(%)',
  average_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '年级平均分',
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (statistics_id) REFERENCES questionnaire_statistics_new(id) ON DELETE CASCADE,
  INDEX idx_statistics_grade (statistics_id, grade_code),
  INDEX idx_questionnaire_grade (questionnaire_id, grade_code)
) COMMENT='年级统计表';

-- 3. 班级统计表
DROP TABLE IF EXISTS class_statistics;
CREATE TABLE class_statistics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  statistics_id INT NOT NULL COMMENT '主统计表ID',
  questionnaire_id INT NOT NULL COMMENT '问卷ID',
  sso_school_code VARCHAR(50) NOT NULL COMMENT '学校编码',
  
  grade_code VARCHAR(10) NOT NULL COMMENT '年级编码',
  grade_name VARCHAR(50) NOT NULL COMMENT '年级名称',
  class_code VARCHAR(10) NOT NULL COMMENT '班级编码',
  class_name VARCHAR(50) NOT NULL COMMENT '班级名称',
  total_students INT DEFAULT 0 COMMENT '班级总学生数',
  submitted_count INT DEFAULT 0 COMMENT '已提交数量',
  completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率(%)',
  average_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '班级平均分',
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (statistics_id) REFERENCES questionnaire_statistics_new(id) ON DELETE CASCADE,
  INDEX idx_statistics_class (statistics_id, grade_code, class_code),
  INDEX idx_questionnaire_class (questionnaire_id, grade_code, class_code)
) COMMENT='班级统计表';

-- 4. 教师统计表
DROP TABLE IF EXISTS teacher_statistics;
CREATE TABLE teacher_statistics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  statistics_id INT NOT NULL COMMENT '主统计表ID',
  questionnaire_id INT NOT NULL COMMENT '问卷ID',
  sso_school_code VARCHAR(50) NOT NULL COMMENT '学校编码',
  
  sso_teacher_id VARCHAR(50) NOT NULL COMMENT '教师ID',
  sso_teacher_name VARCHAR(100) NOT NULL COMMENT '教师姓名',
  sso_teacher_subject VARCHAR(50) NULL COMMENT '教师科目',
  sso_teacher_department VARCHAR(100) NULL COMMENT '教师部门',
  
  evaluation_count INT DEFAULT 0 COMMENT '评价数量',
  total_score DECIMAL(10,2) DEFAULT 0.00 COMMENT '总分',
  average_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '平均分',
  ranking INT DEFAULT 0 COMMENT '排名',
  
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (statistics_id) REFERENCES questionnaire_statistics_new(id) ON DELETE CASCADE,
  INDEX idx_statistics_teacher (statistics_id, sso_teacher_id),
  INDEX idx_questionnaire_teacher (questionnaire_id, sso_teacher_id),
  INDEX idx_ranking (statistics_id, ranking),
  INDEX idx_average_score (statistics_id, average_score DESC)
) COMMENT='教师统计表';

-- 5. 保持未填写学生缓存表不变（已经是合理的设计）
-- incomplete_students_cache 表结构已经合理，无需修改

-- 6. 数据迁移（如果需要保留现有数据）
-- INSERT INTO questionnaire_statistics_new (questionnaire_id, sso_school_code, month, ...)
-- SELECT questionnaire_id, sso_school_code, month, ... FROM questionnaire_statistics;

-- 7. 重命名表（谨慎操作，建议先备份）
-- RENAME TABLE questionnaire_statistics TO questionnaire_statistics_old;
-- RENAME TABLE questionnaire_statistics_new TO questionnaire_statistics;

COMMIT;
