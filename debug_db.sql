-- 查询张妍美学生的问卷响应记录
SELECT 
  r.id,
  r.sso_student_code,
  r.sso_student_name,
  r.sso_student_class,
  r.sso_student_grade,
  r.grade_code,
  r.class_code,
  r.month,
  r.is_completed,
  r.questionnaire_id,
  q.sso_school_code,
  q.title
FROM responses r
JOIN questionnaires q ON r.questionnaire_id = q.id
WHERE r.sso_student_name = '张妍美'
  AND r.month = '2025-06'
ORDER BY r.created_at DESC;

-- 查询该学校2025-06月份的所有问卷响应
SELECT 
  COUNT(*) as total_responses,
  COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed_responses,
  COUNT(DISTINCT sso_student_code) as unique_students
FROM responses r
JOIN questionnaires q ON r.questionnaire_id = q.id
WHERE q.sso_school_code = 'your_school_code' -- 请替换为实际学校编码
  AND r.month = '2025-06';

-- 查询该学校的问卷信息
SELECT 
  id,
  title,
  month,
  sso_school_code,
  status
FROM questionnaires
WHERE month = '2025-06'
ORDER BY created_at DESC;
