// 测试统计修复的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:7001/api';
const SCHOOL_CODE = '17157';
const MONTH = '2025-06';

async function testStatistics() {
  try {
    console.log('🔍 测试统计修复...\n');

    // 1. 测试学校统计
    console.log('1. 获取学校统计数据...');
    const schoolStats = await axios.get(`${BASE_URL}/statistics/school`, {
      params: {
        sso_school_code: SCHOOL_CODE,
        month: MONTH
      }
    });

    console.log('学校统计结果:', {
      total_responses: schoolStats.data.data.total_responses,
      completed_responses: schoolStats.data.data.completed_responses,
      completion_rate: schoolStats.data.data.completion_rate,
      school_average_score: schoolStats.data.data.school_average_score
    });

    // 2. 测试未填写学生列表（第一页）
    console.log('\n2. 获取未填写学生列表（第一页）...');
    const incompleteStudents = await axios.get(`${BASE_URL}/statistics/incomplete-students`, {
      params: {
        sso_school_code: SCHOOL_CODE,
        month: MONTH,
        page: 1,
        pageSize: 5
      }
    });

    console.log('未填写学生统计:', {
      total: incompleteStudents.data.data.total,
      page: incompleteStudents.data.data.page,
      pageSize: incompleteStudents.data.data.pageSize,
      totalPages: incompleteStudents.data.data.totalPages
    });

    console.log('\n前5个未填写学生:');
    incompleteStudents.data.data.list.slice(0, 5).forEach((student, index) => {
      console.log(`${index + 1}. ${student.sso_student_name} - ${student.grade_name}${student.class_name}`);
    });

    // 3. 检查数据一致性
    const schoolTotal = schoolStats.data.data.total_responses || 0;
    const schoolCompleted = schoolStats.data.data.completed_responses || 0;
    const incompleteTotal = incompleteStudents.data.data.total || 0;
    
    console.log('\n📊 数据一致性检查:');
    console.log(`学校总学生数: ${schoolTotal}`);
    console.log(`已完成数: ${schoolCompleted}`);
    console.log(`未完成数: ${incompleteTotal}`);
    console.log(`计算的未完成数: ${schoolTotal - schoolCompleted}`);
    
    const isConsistent = (schoolTotal - schoolCompleted) === incompleteTotal;
    console.log(`数据是否一致: ${isConsistent ? '✅ 是' : '❌ 否'}`);

    if (!isConsistent) {
      console.log('\n🔍 数据不一致，可能的原因:');
      console.log('1. 年级班级编码提取不一致');
      console.log('2. 学生编码匹配问题');
      console.log('3. 数据源不同步');
    }

    // 4. 查看具体的年级班级分布
    console.log('\n4. 查看年级班级分布...');
    if (incompleteStudents.data.data.summary) {
      console.log('年级分布:', incompleteStudents.data.data.summary.by_grade);
      console.log('班级分布（前5个）:', incompleteStudents.data.data.summary.by_class.slice(0, 5));
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testStatistics();
