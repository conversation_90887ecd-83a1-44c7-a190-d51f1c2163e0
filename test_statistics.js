const axios = require('axios');

async function testStatistics() {
  try {
    // 测试获取未填写学生列表
    const response = await axios.get('http://localhost:7001/api/statistics/incomplete-students', {
      params: {
        sso_school_code: 'your_school_code', // 请替换为实际的学校编码
        month: '2025-06'
      }
    });
    
    console.log('统计结果:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('请求失败:', error.response?.data || error.message);
  }
}

testStatistics();
