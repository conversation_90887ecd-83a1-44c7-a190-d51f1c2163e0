const axios = require('axios');

async function testDebug() {
  try {
    console.log('开始调试学生匹配问题...');

    // 调试张妍美学生的匹配问题
    const debugResponse = await axios.get('http://localhost:3141/api/statistics/debug-student-matching', {
      params: {
        sso_school_code: 'test_school', // 请替换为实际的学校编码
        student_name: '张妍美',
        month: '2025-06'
      },
      timeout: 30000
    });

    console.log('调试结果:', JSON.stringify(debugResponse.data, null, 2));

  } catch (error) {
    console.error('请求失败:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      code: error.code,
      config: error.config ? {
        url: error.config.url,
        method: error.config.method,
        params: error.config.params
      } : null
    });
  }
}

testDebug();
