import { Controller, Get, Post, Inject, Query, Param, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Validate, Rule, RuleType } from '@midwayjs/validate';
import { StatisticsService } from '../service/statistics.service';
import {
  SchoolStatisticsQueryDTO,
  TeacherStatisticsQueryDTO,
  TeacherRankingQueryDTO,
  TrendAnalysisQueryDTO,
  IncompleteStudentsQueryDTO,
} from '../dto/statistics.dto';
import { ErrorCode } from '../common/ErrorCode';

/**
 * 触发统计计算DTO
 */
class TriggerStatisticsDTO {
  @Rule(RuleType.number().integer().min(1).required())
  questionnaire_id: number;
}

/**
 * 未填写学生查询DTO（基于缓存）
 */
class CachedIncompleteStudentsQueryDTO {
  @Rule(RuleType.number().integer().min(1).required())
  questionnaire_id: number;

  @Rule(RuleType.string().max(20).optional())
  grade_code?: string;

  @Rule(RuleType.string().max(20).optional())
  class_code?: string;

  @Rule(RuleType.number().integer().min(1).default(1))
  page?: number;

  @Rule(RuleType.number().integer().min(1).max(100).default(20))
  pageSize?: number;
}

@Controller('/api/statistics')
export class StatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  statisticsService: StatisticsService;

  /**
   * 获取学校维度统计
   */
  @Get('/school')
  @Validate()
  async getSchoolStatistics(@Query() queryDto: SchoolStatisticsQueryDTO) {
    try {
      this.ctx.logger.info('获取学校统计数据请求', {
        sso_school_code: queryDto.sso_school_code,
        month: queryDto.month,
        start_month: queryDto.start_month,
        end_month: queryDto.end_month,
      });

      const statistics = await this.statisticsService.getSchoolStatistics(
        queryDto
      );

      return statistics;
    } catch (error) {
      this.ctx.logger.error('获取学校统计数据失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取教师维度统计
   */
  @Get('/teacher')
  @Validate()
  async getTeacherStatistics(@Query() queryDto: TeacherStatisticsQueryDTO) {
    try {
      this.ctx.logger.info('获取教师统计数据请求', {
        sso_teacher_id: queryDto.sso_teacher_id,
        sso_school_code: queryDto.sso_school_code,
        month: queryDto.month,
      });

      const statistics = await this.statisticsService.getTeacherStatistics(
        queryDto
      );

      return statistics;
    } catch (error) {
      this.ctx.logger.error('获取教师统计数据失败', error, {
        sso_teacher_id: queryDto.sso_teacher_id,
      });

      throw error;
    }
  }

  /**
   * 获取教师排名
   */
  @Get('/teacher-ranking')
  @Validate()
  async getTeacherRanking(@Query() queryDto: TeacherRankingQueryDTO) {
    try {
      this.ctx.logger.info('获取教师排名请求', {
        sso_school_code: queryDto.sso_school_code,
        month: queryDto.month,
        subject: queryDto.subject,
        department: queryDto.department,
        sort_by: queryDto.sort_by,
      });

      const ranking = await this.statisticsService.getTeacherRanking(queryDto);

      return ranking;
    } catch (error) {
      this.ctx.logger.error('获取教师排名失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取趋势分析数据
   */
  @Get('/trend')
  @Validate()
  async getTrendAnalysis(@Query() queryDto: TrendAnalysisQueryDTO) {
    try {
      this.ctx.logger.info('获取趋势分析数据请求', {
        sso_school_code: queryDto.sso_school_code,
        start_month: queryDto.start_month,
        end_month: queryDto.end_month,
        analysis_type: queryDto.analysis_type,
      });

      const trendData = await this.statisticsService.getTrendAnalysis(queryDto);

      return trendData;
    } catch (error) {
      this.ctx.logger.error('获取趋势分析数据失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取未填写学生名单（分页）
   */
  @Get('/incomplete-students')
  @Validate()
  async getIncompleteStudents(@Query() queryDto: IncompleteStudentsQueryDTO) {
    try {
      this.ctx.logger.info('获取未填写学生名单请求', {
        sso_school_code: queryDto.sso_school_code,
        questionnaire_id: queryDto.questionnaire_id,
        month: queryDto.month,
        page: queryDto.page,
        pageSize: queryDto.pageSize,
        grade_code: queryDto.grade_code,
        class_code: queryDto.class_code,
      });

      const incompleteStudents =
        await this.statisticsService.getIncompleteStudentsWithPagination(
          queryDto.sso_school_code,
          queryDto.questionnaire_id,
          queryDto.month,
          queryDto.page || 1,
          queryDto.pageSize || 20,
          queryDto.grade_code,
          queryDto.class_code
        );

      return incompleteStudents;
    } catch (error) {
      this.ctx.logger.error('获取未填写学生名单失败', error, {
        sso_school_code: queryDto.sso_school_code,
      });

      throw error;
    }
  }

  /**
   * 获取教师评分分布
   */
  @Get('/teacher/:teacherId/distribution')
  async getTeacherScoreDistribution(
    @Param('teacherId') teacherId: string,
    @Query() query: { sso_school_code?: string; month?: string }
  ) {
    try {
      const { sso_school_code, month } = query;

      this.ctx.logger.info('获取教师评分分布请求', {
        sso_teacher_id: teacherId,
        sso_school_code,
        month,
      });

      const distribution = await this.statisticsService.getScoreDistribution(
        teacherId,
        sso_school_code,
        month
      );

      return distribution;
    } catch (error) {
      this.ctx.logger.error('获取教师评分分布失败', error, {
        sso_teacher_id: teacherId,
      });

      throw error;
    }
  }

  /**
   * 获取教师关键词云
   */
  @Get('/teacher/:teacherId/keywords')
  async getTeacherKeywords(
    @Param('teacherId') teacherId: string,
    @Query() query: { sso_school_code?: string; month?: string }
  ) {
    try {
      const { sso_school_code, month } = query;

      this.ctx.logger.info('获取教师关键词云请求', {
        sso_teacher_id: teacherId,
        sso_school_code,
        month,
      });

      const keywords = await this.statisticsService.getKeywordCloud(
        teacherId,
        sso_school_code,
        month
      );

      return keywords;
    } catch (error) {
      this.ctx.logger.error('获取教师关键词云失败', error, {
        sso_teacher_id: teacherId,
      });

      throw error;
    }
  }

  /**
   * 获取学校响应趋势
   */
  @Get('/school/:schoolId/trend')
  async getSchoolTrend(
    @Param('schoolId') schoolId: string,
    @Query() query: { start_month: string; end_month: string }
  ) {
    try {
      const { start_month, end_month } = query;

      if (!start_month || !end_month) {
        return {
          errCode: ErrorCode.PARAM_ERROR,
          msg: '开始月份和结束月份是必填参数',
          data: null,
        };
      }

      this.ctx.logger.info('获取学校响应趋势请求', {
        sso_school_code: schoolId,
        start_month,
        end_month,
      });

      const trendData = await this.statisticsService.getResponseTrend(
        schoolId,
        start_month,
        end_month
      );

      return trendData;
    } catch (error) {
      this.ctx.logger.error('获取学校响应趋势失败', error, {
        sso_school_code: schoolId,
      });

      throw error;
    }
  }

  /**
   * 获取教师评价趋势
   */
  @Get('/teacher/:teacherId/trend')
  async getTeacherTrend(
    @Param('teacherId') teacherId: string,
    @Query()
    query: {
      sso_school_code?: string;
      start_month?: string;
      end_month?: string;
    }
  ) {
    try {
      const { sso_school_code, start_month, end_month } = query;

      this.ctx.logger.info('获取教师评价趋势请求', {
        sso_teacher_id: teacherId,
        sso_school_code,
        start_month,
        end_month,
      });

      const trendData = await this.statisticsService.getTeacherTrend(
        teacherId,
        sso_school_code,
        start_month,
        end_month
      );

      return trendData;
    } catch (error) {
      this.ctx.logger.error('获取教师评价趋势失败', error, {
        sso_teacher_id: teacherId,
      });

      throw error;
    }
  }

  // ==================== 缓存统计API ====================

  /**
   * 触发统计计算
   */
  @Post('/trigger')
  @Validate()
  async triggerStatistics(@Body() triggerDto: TriggerStatisticsDTO) {
    try {
      // 从JWT或session中获取用户ID，这里暂时使用固定值
      const triggeredBy = this.ctx.state?.user?.id || 'system';

      this.ctx.logger.info('触发统计计算请求', {
        questionnaire_id: triggerDto.questionnaire_id,
        triggered_by: triggeredBy,
      });

      const statistics =
        await this.statisticsService.triggerStatisticsCalculation(
          triggerDto.questionnaire_id,
          triggeredBy
        );

      return {
        errCode: 0,
        msg: '统计任务已启动',
        data: {
          statistics_id: statistics.id,
          questionnaire_id: statistics.questionnaire_id,
          status: statistics.status,
          triggered_by: statistics.triggered_by,
          created_at: statistics.created_at,
        },
      };
    } catch (error) {
      this.ctx.logger.error('触发统计计算失败', {
        questionnaire_id: triggerDto.questionnaire_id,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '触发统计计算失败',
        data: null,
      };
    }
  }

  /**
   * 获取统计状态
   */
  @Get('/status/:questionnaireId')
  async getStatisticsStatus(@Param('questionnaireId') questionnaireId: number) {
    try {
      this.ctx.logger.info('获取统计状态请求', {
        questionnaire_id: questionnaireId,
      });

      const statistics = await this.statisticsService.getStatisticsStatus(
        questionnaireId
      );

      if (!statistics) {
        return {
          errCode: 0,
          msg: '暂无统计数据',
          data: {
            status: 'not_started',
            message: '尚未开始统计计算',
          },
        };
      }

      return {
        errCode: 0,
        msg: '获取统计状态成功',
        data: {
          statistics_id: statistics.id,
          questionnaire_id: statistics.questionnaire_id,
          status: statistics.status,
          last_calculated_at: statistics.last_calculated_at,
          calculation_duration: statistics.calculation_duration,
          error_message: statistics.error_message,
          triggered_by: statistics.triggered_by,
          total_students: statistics.total_students,
          submitted_count: statistics.submitted_count,
          completion_rate: statistics.completion_rate,
        },
      };
    } catch (error) {
      this.ctx.logger.error('获取统计状态失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取统计状态失败',
        data: null,
      };
    }
  }

  /**
   * 获取缓存的统计数据
   */
  @Get('/cached/:questionnaireId')
  async getCachedStatistics(@Param('questionnaireId') questionnaireId: number) {
    try {
      this.ctx.logger.info('获取缓存统计数据请求', {
        questionnaire_id: questionnaireId,
      });

      const cachedData = await this.statisticsService.getCachedStatistics(
        questionnaireId
      );

      if (!cachedData) {
        return {
          errCode: 0,
          msg: '暂无缓存数据，请先触发统计计算',
          data: null,
        };
      }

      return {
        errCode: 0,
        msg: '获取缓存统计数据成功',
        data: cachedData,
      };
    } catch (error) {
      this.ctx.logger.error('获取缓存统计数据失败', {
        questionnaire_id: questionnaireId,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取缓存统计数据失败',
        data: null,
      };
    }
  }

  /**
   * 获取缓存的未填写学生列表（分页）
   */
  @Get('/cached-incomplete-students')
  @Validate()
  async getCachedIncompleteStudents(
    @Query() queryDto: CachedIncompleteStudentsQueryDTO
  ) {
    try {
      this.ctx.logger.info('获取缓存未填写学生列表请求', {
        questionnaire_id: queryDto.questionnaire_id,
        grade_code: queryDto.grade_code,
        class_code: queryDto.class_code,
        page: queryDto.page,
        pageSize: queryDto.pageSize,
      });

      const result = await this.statisticsService.getCachedIncompleteStudents(
        queryDto.questionnaire_id,
        queryDto
      );

      return {
        errCode: 0,
        msg: '获取未填写学生列表成功',
        data: result,
      };
    } catch (error) {
      this.ctx.logger.error('获取缓存未填写学生列表失败', {
        questionnaire_id: queryDto.questionnaire_id,
        error: error.message,
      });

      return {
        errCode: 1,
        msg: error.message || '获取未填写学生列表失败',
        data: null,
      };
    }
  }
}
