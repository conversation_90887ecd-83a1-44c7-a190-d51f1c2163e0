# Excel导出功能API文档

## 概述

基于当前的统计分析系统，实现了完整的Excel导出功能，支持多种数据类型的导出，包括学校统计、教师统计、教师排名、问卷响应、综合报表和未完成学生名单等。

## 功能特点

- 🔄 **多格式支持**: 支持XLSX格式导出
- 📊 **多工作表**: 根据数据类型自动创建多个工作表
- 🎨 **美观格式**: 自动设置表头样式、列宽、数据格式
- 📈 **完整数据**: 包含统计分析、趋势数据、排名信息等
- 🔍 **灵活筛选**: 支持按学校、月份、教师等多维度筛选

## API接口

### 1. 导出学校统计数据

**接口地址**: `POST /api/export/school-statistics`

**请求参数**:
```json
{
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_trend": true,
  "include_teacher_ranking": true,
  "include_incomplete_students": true
}
```

**响应**: 直接返回Excel文件流

**生成的工作表**:
- 学校基础统计
- 趋势分析 (可选)
- 教师排名 (可选)
- 未完成统计 (可选)

### 2. 导出教师统计数据

**接口地址**: `POST /api/export/teacher-statistics`

**请求参数**:
```json
{
  "sso_school_code": "SCHOOL001",
  "sso_teacher_id": "TEACHER001",
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_distribution": true,
  "include_keywords": true,
  "include_trend": true
}
```

**生成的工作表**:
- 教师基础统计
- 评分分布 (可选)
- 关键词分析 (可选)
- 评价趋势 (可选)

### 3. 导出教师排名数据

**接口地址**: `POST /api/export/teacher-ranking`

**请求参数**:
```json
{
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "subject": "数学",
  "department": "初中部",
  "sort_by": "average_score",
  "sort_order": "DESC",
  "limit": 100,
  "export_format": "xlsx"
}
```

**生成的工作表**:
- 教师排名

### 4. 导出问卷响应数据

**接口地址**: `POST /api/export/questionnaire-responses`

**请求参数**:
```json
{
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 1,
  "month": "2024-03",
  "grade_code": "7",
  "class_code": "1",
  "is_completed": true,
  "include_answers": true,
  "export_format": "xlsx"
}
```

**生成的工作表**:
- 问卷响应
- 评价详情 (可选)

### 5. 导出综合报表

**接口地址**: `POST /api/export/comprehensive-report`

**请求参数**:
```json
{
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "start_month": "2024-01",
  "end_month": "2024-03",
  "export_format": "xlsx",
  "include_school_summary": true,
  "include_teacher_ranking": true,
  "include_grade_analysis": true,
  "include_subject_analysis": true,
  "include_trend_analysis": true,
  "include_completion_analysis": true
}
```

**生成的工作表**:
- 学校概览 (可选)
- 教师排名 (可选)
- 趋势分析 (可选)
- 完成情况分析 (可选)

### 6. 导出未完成学生名单

**接口地址**: `POST /api/export/incomplete-students`

**请求参数**:
```json
{
  "sso_school_code": "SCHOOL001",
  "questionnaire_id": 1,
  "month": "2024-03",
  "grade_code": "7",
  "class_code": "1",
  "include_contact_info": true,
  "export_format": "xlsx"
}
```

**生成的工作表**:
- 未完成学生名单
- 统计汇总

### 7. 通用导出接口

**接口地址**: `POST /api/export/general`

**请求参数**:
```json
{
  "export_type": "school_statistics",
  "sso_school_code": "SCHOOL001",
  "month": "2024-03",
  "export_format": "xlsx"
}
```

**支持的导出类型**:
- `school_statistics`: 学校统计
- `teacher_statistics`: 教师统计
- `teacher_ranking`: 教师排名
- `questionnaire_responses`: 问卷响应
- `comprehensive_report`: 综合报表
- `incomplete_students`: 未完成学生

## 参数说明

### 公共参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sso_school_code | string | 是 | 学校编码 |
| month | string | 否 | 统计月份，格式：YYYY-MM |
| start_month | string | 否 | 开始月份，格式：YYYY-MM |
| end_month | string | 否 | 结束月份，格式：YYYY-MM |
| export_format | string | 否 | 导出格式，默认xlsx |

### 筛选参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| questionnaire_id | number | 否 | 问卷ID |
| sso_teacher_id | string | 否 | 教师ID |
| grade_code | string | 否 | 年级编码 |
| class_code | string | 否 | 班级编码 |
| subject | string | 否 | 科目 |
| department | string | 否 | 部门 |

### 选项参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| include_trend | boolean | 否 | 是否包含趋势数据 |
| include_teacher_ranking | boolean | 否 | 是否包含教师排名 |
| include_distribution | boolean | 否 | 是否包含评分分布 |
| include_keywords | boolean | 否 | 是否包含关键词分析 |
| include_answers | boolean | 否 | 是否包含答案详情 |
| include_contact_info | boolean | 否 | 是否包含联系信息 |

## 响应格式

成功时直接返回Excel文件流，浏览器会自动下载文件。

失败时返回JSON格式错误信息：
```json
{
  "errCode": 500,
  "msg": "导出失败的具体原因",
  "data": null
}
```

## 使用示例

### JavaScript/Ajax示例

```javascript
// 导出学校统计数据
function exportSchoolStatistics() {
  const data = {
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    include_trend: true,
    include_teacher_ranking: true
  };

  fetch('/api/export/school-statistics', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  })
  .then(response => {
    if (response.ok) {
      return response.blob();
    }
    throw new Error('导出失败');
  })
  .then(blob => {
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '学校统计报表.xlsx';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  })
  .catch(error => {
    console.error('导出失败:', error);
  });
}
```

### cURL示例

```bash
# 导出教师排名数据
curl -X POST "http://localhost:7001/api/export/teacher-ranking" \
  -H "Content-Type: application/json" \
  -d '{
    "sso_school_code": "SCHOOL001",
    "month": "2024-03",
    "sort_by": "average_score",
    "sort_order": "DESC",
    "limit": 50
  }' \
  --output "教师排名报表.xlsx"
```

## 注意事项

1. **文件大小限制**: 大量数据导出时可能需要较长时间，建议设置合理的超时时间
2. **内存使用**: Excel文件生成过程中会占用一定内存，建议监控服务器资源使用情况
3. **并发限制**: 建议对导出接口设置适当的并发限制，避免服务器过载
4. **权限控制**: 实际使用时应添加适当的权限验证，确保用户只能导出有权限的数据
5. **文件名编码**: 文件名包含中文时已进行URL编码处理，确保下载时文件名正确显示

## 扩展功能

系统支持进一步扩展：

1. **自定义模板**: 可以根据需要自定义Excel模板样式
2. **多格式支持**: 可以扩展支持CSV、PDF等其他格式
3. **定时导出**: 可以结合定时任务实现自动导出功能
4. **邮件发送**: 可以将导出的文件通过邮件发送给相关人员
5. **云存储**: 可以将导出的文件上传到云存储服务
